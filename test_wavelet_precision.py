#!/usr/bin/env python3
"""
测试小波变换在不同精度下的重构精度
特别是bf16精度下的表现
"""

import torch
import torch.nn.functional as F
import pywt
import numpy as np

def create_2d_wavelet_filter(wave, in_size, type=torch.float):
    """创建2D小波滤波器"""
    w = pywt.Wavelet(wave)
    dec_hi = torch.tensor(w.dec_hi[::-1], dtype=type)
    dec_lo = torch.tensor(w.dec_lo[::-1], dtype=type)
    dec_filters = torch.stack([dec_lo.unsqueeze(0) * dec_lo.unsqueeze(1),
                               dec_lo.unsqueeze(0) * dec_hi.unsqueeze(1),
                               dec_hi.unsqueeze(0) * dec_lo.unsqueeze(1),
                               dec_hi.unsqueeze(0) * dec_hi.unsqueeze(1)], dim=0)

    dec_filters = dec_filters[:, None].repeat(in_size, 1, 1, 1)

    rec_hi = torch.tensor(w.rec_hi, dtype=type)
    rec_lo = torch.tensor(w.rec_lo, dtype=type)
    rec_filters = torch.stack([rec_lo.unsqueeze(0) * rec_lo.unsqueeze(1),
                               rec_lo.unsqueeze(0) * rec_hi.unsqueeze(1),
                               rec_hi.unsqueeze(0) * rec_lo.unsqueeze(1),
                               rec_hi.unsqueeze(0) * rec_hi.unsqueeze(1)], dim=0)

    rec_filters = rec_filters[:, None].repeat(in_size, 1, 1, 1)

    return dec_filters, rec_filters

def wavelet_2d_transform(x, filters):
    """2D小波变换"""
    b, c, h, w = x.shape
    pad = (filters.shape[2] // 2 - 1, filters.shape[3] // 2 - 1)
    x = F.conv2d(x, filters, stride=2, groups=c, padding=pad)
    x = x.reshape(b, c, 4, h // 2, w // 2)
    return x

def inverse_2d_wavelet_transform(x, filters):
    """2D逆小波变换"""
    b, c, _, h_half, w_half = x.shape
    pad = (filters.shape[2] // 2 - 1, filters.shape[3] // 2 - 1)
    x = x.reshape(b, c * 4, h_half, w_half)
    x = F.conv_transpose2d(x, filters, stride=2, groups=c, padding=pad)
    return x

def test_perfect_reconstruction(dtype=torch.float32, wavelet='db4'):
    """测试完美重构"""
    print(f"\n🧪 测试完美重构 - 精度: {dtype}, 小波: {wavelet}")
    
    # 创建测试数据
    torch.manual_seed(42)
    x_original = torch.randn(1, 3, 128, 256, dtype=dtype)
    
    # 创建小波滤波器
    dec_filters, rec_filters = create_2d_wavelet_filter(wavelet, 3, dtype)
    
    print(f"📊 输入数据:")
    print(f"   形状: {x_original.shape}")
    print(f"   精度: {x_original.dtype}")
    print(f"   范围: [{x_original.min().item():.6f}, {x_original.max().item():.6f}]")
    
    # 前向变换
    x_wt = wavelet_2d_transform(x_original, dec_filters)
    print(f"\n📊 小波变换后:")
    print(f"   形状: {x_wt.shape}")
    print(f"   LL子带范围: [{x_wt[:,:,0,:,:].min().item():.6f}, {x_wt[:,:,0,:,:].max().item():.6f}]")
    
    # 逆变换重构
    x_reconstructed = inverse_2d_wavelet_transform(x_wt, rec_filters)
    
    # 裁剪到原始尺寸
    x_reconstructed = x_reconstructed[:, :, :x_original.shape[2], :x_original.shape[3]]
    
    print(f"\n📊 重构后:")
    print(f"   形状: {x_reconstructed.shape}")
    print(f"   精度: {x_reconstructed.dtype}")
    print(f"   范围: [{x_reconstructed.min().item():.6f}, {x_reconstructed.max().item():.6f}]")
    
    # 计算重构误差
    mse_error = F.mse_loss(x_reconstructed, x_original).item()
    mae_error = F.l1_loss(x_reconstructed, x_original).item()
    max_error = (x_reconstructed - x_original).abs().max().item()
    
    print(f"\n📊 重构误差:")
    print(f"   MSE: {mse_error:.2e}")
    print(f"   MAE: {mae_error:.2e}")
    print(f"   Max: {max_error:.2e}")
    
    # 判断是否为完美重构
    is_perfect = max_error < 1e-5 if dtype == torch.float32 else max_error < 1e-2
    
    if is_perfect:
        print(f"✅ 完美重构成功！")
    else:
        print(f"⚠️  重构误差较大，可能存在精度问题")
    
    return mse_error, mae_error, max_error

def test_multi_level_reconstruction(dtype=torch.float32, levels=2):
    """测试多级小波变换重构"""
    print(f"\n🧪 测试{levels}级小波变换重构 - 精度: {dtype}")
    
    torch.manual_seed(42)
    x_original = torch.randn(1, 3, 128, 256, dtype=dtype)
    
    dec_filters, rec_filters = create_2d_wavelet_filter('db4', 3, dtype)
    
    # 多级分解
    x_ll_levels = []
    x_h_levels = []
    shapes = []
    
    curr_x_ll = x_original
    
    for level in range(levels):
        curr_shape = curr_x_ll.shape
        shapes.append(curr_shape)
        
        # 处理奇数尺寸
        if (curr_shape[2] % 2 > 0) or (curr_shape[3] % 2 > 0):
            curr_pads = (0, curr_shape[3] % 2, 0, curr_shape[2] % 2)
            curr_x_ll = F.pad(curr_x_ll, curr_pads)
        
        # 小波变换
        curr_x = wavelet_2d_transform(curr_x_ll, dec_filters)
        curr_x_ll = curr_x[:,:,0,:,:]  # LL子带
        
        x_ll_levels.append(curr_x_ll)
        x_h_levels.append(curr_x[:,:,1:4,:,:])  # LH, HL, HH子带
        
        print(f"   Level {level+1}: {curr_x_ll.shape}")
    
    # 多级重构
    next_x_ll = x_ll_levels[-1]  # 从最深层开始
    
    for level in range(levels-1, -1, -1):
        curr_x_h = x_h_levels[level]
        curr_shape = shapes[level]
        
        # 重组4个子带
        if level == levels-1:
            curr_x_ll = next_x_ll
        else:
            curr_x_ll = next_x_ll
        
        curr_x = torch.cat([curr_x_ll.unsqueeze(2), curr_x_h], dim=2)
        
        # 逆小波变换
        next_x_ll = inverse_2d_wavelet_transform(curr_x, rec_filters)
        
        # 裁剪到原始尺寸
        next_x_ll = next_x_ll[:, :, :curr_shape[2], :curr_shape[3]]
        
        print(f"   重构 Level {level+1}: {next_x_ll.shape}")
    
    x_reconstructed = next_x_ll
    
    # 计算误差
    mse_error = F.mse_loss(x_reconstructed, x_original).item()
    mae_error = F.l1_loss(x_reconstructed, x_original).item()
    max_error = (x_reconstructed - x_original).abs().max().item()
    
    print(f"\n📊 {levels}级重构误差:")
    print(f"   MSE: {mse_error:.2e}")
    print(f"   MAE: {mae_error:.2e}")
    print(f"   Max: {max_error:.2e}")
    
    return mse_error, mae_error, max_error

def compare_precision_types():
    """对比不同精度类型"""
    print("🔍 对比不同精度下的小波重构")
    print("=" * 60)
    
    precisions = [
        torch.float32,
        torch.float16, 
        torch.bfloat16
    ]
    
    wavelets = ['db1', 'db4', 'haar']
    
    results = {}
    
    for precision in precisions:
        results[precision] = {}
        for wavelet in wavelets:
            try:
                mse, mae, max_err = test_perfect_reconstruction(precision, wavelet)
                results[precision][wavelet] = (mse, mae, max_err)
            except Exception as e:
                print(f"❌ {precision} + {wavelet} 失败: {str(e)}")
                results[precision][wavelet] = (float('inf'), float('inf'), float('inf'))
    
    # 打印对比表格
    print("\n📊 精度对比表格:")
    print("=" * 60)
    print(f"{'精度':<12} {'小波':<8} {'MSE':<12} {'MAE':<12} {'Max Error':<12}")
    print("-" * 60)
    
    for precision in precisions:
        for wavelet in wavelets:
            if wavelet in results[precision]:
                mse, mae, max_err = results[precision][wavelet]
                print(f"{str(precision):<12} {wavelet:<8} {mse:<12.2e} {mae:<12.2e} {max_err:<12.2e}")
    
    return results

if __name__ == "__main__":
    print("🌊 小波变换精度测试")
    print("=" * 60)
    
    # 测试不同精度的完美重构
    compare_precision_types()
    
    # 测试多级变换
    print("\n" + "=" * 60)
    test_multi_level_reconstruction(torch.float32, levels=2)
    test_multi_level_reconstruction(torch.bfloat16, levels=2)
    
    print("\n🎯 结论:")
    print("1. float32: 应该有完美重构")
    print("2. bfloat16: 可能有精度损失，但应该在可接受范围内")
    print("3. 多级变换会累积误差")
    print("4. 不同小波基的数值稳定性不同")
