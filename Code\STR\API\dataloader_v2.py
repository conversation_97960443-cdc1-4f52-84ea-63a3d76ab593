import os
import numpy as np
import torch
from torch.utils.data import Dataset, DataLoader

class Gremlin(Dataset):
    def __init__(self, file_list, transform=None):
        self.transform = transform
        self.samples = file_list
        if not self.samples:
            # This allows an "empty" dataset if no files are provided
            self.xshape = None 
            self.tshape = None
            return # Important to return if samples is empty, to avoid error in _get_shape
        
        self.samples.sort()
        # _get_shape is called here. It ASSUMES:
        # 1. self.samples[0] exists and is a valid .npz file.
        # 2. Inside that .npz: data['xdata'] is (H, W, C_in)
        # 3. Inside that .npz: data['ydata'] is (H, W)
        # If these assumptions are wrong for the first file, this will error.
        try:
            self.xshape, self.tshape = self._get_shape()
        except Exception as e: # General exception for issues in _get_shape
            first_sample = self.samples[0] # samples is guaranteed not empty here
            raise RuntimeError(f"Error in _get_shape() for first sample '{first_sample}': {e}. Check .npz content and Gremlin._get_shape assumptions.")

    def _get_shape(self):
        with np.load(self.samples[0]) as data:
            x_np = data['xdata']
            t_np = data['ydata']
            xshape = np.moveaxis(x_np, -1, 0).shape
            tshape = np.expand_dims(t_np, axis=0).shape
        return xshape, tshape

    def __len__(self):
        return len(self.samples)

    def __getitem__(self, idx):
        f = self.samples[idx]
        try:
            with np.load(f) as data:
                x_np = data['xdata']
                t_np = data['ydata']
        except Exception as e:
            raise IOError(f"Error loading or processing {f}: {e}")

        x_np_transformed = np.moveaxis(x_np, -1, 0)
        t_np_transformed = np.expand_dims(t_np, axis=0)

        x = torch.from_numpy(x_np_transformed).float()
        t = torch.from_numpy(t_np_transformed).float()

        if self.transform is not None:
            x = self.transform(x)
            t = self.transform(t)
        return x, t

def load_data(batch_size, val_batch_size, test_batch_size, dataname, num_workers, **kwargs):
    # 根据dataname确定文件列表路径
    base_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'datalist')
    dataset_dir = os.path.join(base_dir, dataname)
    train_list = os.path.join(dataset_dir, f'{dataname}_train.txt')
    val_list = os.path.join(dataset_dir, f'{dataname}_vail.txt')
    test_list = os.path.join(dataset_dir, f'{dataname}_test.txt')

    transform = None

    # 加载训练数据集
    train_dataset = None
    if os.path.exists(train_list):
        with open(train_list, 'r') as f:
            train_files = [line.strip() for line in f if line.strip() and not line.startswith('#')]
        if train_files:
            train_dataset = Gremlin(file_list=train_files, transform=transform)
            if train_dataset.xshape is None:
                train_dataset = None

    # 加载验证数据集
    val_dataset = None
    if os.path.exists(val_list):
        with open(val_list, 'r') as f:
            val_files = [line.strip() for line in f if line.strip() and not line.startswith('#')]
        if val_files:
            val_dataset = Gremlin(file_list=val_files, transform=transform)
            if val_dataset.xshape is None:
                val_dataset = None

    # 加载测试数据集
    test_dataset = None
    if os.path.exists(test_list):
        with open(test_list, 'r') as f:
            test_files = [line.strip() for line in f if line.strip() and not line.startswith('#')]
        if test_files:
            test_dataset = Gremlin(file_list=test_files, transform=transform)
            if test_dataset.xshape is None:
                test_dataset = None

    # 创建数据加载器
    dataloader_train = None
    if train_dataset and len(train_dataset) > 0:
        dataloader_train = DataLoader(
            train_dataset, batch_size=batch_size, shuffle=True,
            num_workers=num_workers, pin_memory=True
        )

    dataloader_validation = None
    if val_dataset and len(val_dataset) > 0:
        dataloader_validation = DataLoader(
            val_dataset, batch_size=val_batch_size, shuffle=False,
            num_workers=num_workers, pin_memory=True
        )

    dataloader_test = None
    if test_dataset and len(test_dataset) > 0:
        dataloader_test = DataLoader(
            test_dataset, batch_size=test_batch_size, shuffle=False,
            num_workers=num_workers, pin_memory=True
        )

    max_val = 60
    min_val = 0

    return dataloader_train, dataloader_validation, dataloader_test, max_val, min_val

if __name__ == '__main__':
    # 测试代码
    args_batch_size = 2
    args_val_batch_size = 2
    args_dataname = 'us2020'
    args_num_workers = 0

    print(f"--- dataloader_v2.py test ---")
    print(f"Test Settings: DataName={args_dataname}")
    print(f"               BatchSize={args_batch_size}, ValBatchSize={args_val_batch_size}")

    train_loader, vali_loader, test_loader, max_val, min_val = load_data(
        batch_size=args_batch_size,
        val_batch_size=args_val_batch_size,
        dataname=args_dataname,
        num_workers=args_num_workers
    )

    print(f"\n--- Train DataLoader Output ---")
    if train_loader:
        print(f"Train Dataset samples: {len(train_loader.dataset)}")
        if hasattr(train_loader.dataset, 'xshape'):
            print(f"  Inferred xshape: {train_loader.dataset.xshape}, tshape: {train_loader.dataset.tshape}")
    else:
        print("Train DataLoader is None")

    print(f"\n--- Validation DataLoader Output ---")
    if vali_loader:
        print(f"Validation Dataset samples: {len(vali_loader.dataset)}")
        if hasattr(vali_loader.dataset, 'xshape'):
            print(f"  Inferred xshape: {vali_loader.dataset.xshape}, tshape: {vali_loader.dataset.tshape}")
    else:
        print("Validation DataLoader is None")

    print(f"\n--- Test DataLoader Output ---")
    if test_loader:
        print(f"Test Dataset samples: {len(test_loader.dataset)}")
        if hasattr(test_loader.dataset, 'xshape'):
            print(f"  Inferred xshape: {test_loader.dataset.xshape}, tshape: {test_loader.dataset.tshape}")
    else:
        print("Test DataLoader is None")