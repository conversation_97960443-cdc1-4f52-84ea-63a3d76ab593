2025-05-20 18:18:10,077 - 
res_dir: 	./520	
ex_name: 	UNet_hmse_lion_128	
seed: 	42	
batch_size: 	16	
val_batch_size: 	8	
test_batch_size: 	1	
data_root: 	/Storage01/ShareData/radar_us/STR_US/counts/	
dataname: 	us2020	
num_workers: 	8	
in_shape: 	[4, 768, 1536]	
out_chans: 	1	
unet_channels: 	[32, 64, 128, 256]	
unet_skip: 	True	
epochs: 	100	
log_step: 	1	
lr: 	0.001	
early_stopping_patience: 	15	
scheduler: 	cosine	
optimizer: 	lion	
warmup_steps: 	1000	
loss_type: 	hmse	
metrics_thresholds: 	[10, 20, 30, 40]	
mixed_precision: 	fp16	
gradient_accumulation_steps: 	8	
gradient_clipping: 	1.0	
2025-05-20 18:18:10,077 - Using mixed precision: fp16
2025-05-20 18:18:10,078 - Number of processes: 1
2025-05-20 18:18:10,078 - Distributed type: DistributedType.NO
2025-05-20 18:18:11,489 - Using loss function: hmse
2025-05-20 18:31:05,374 - Epoch: 1 | Train Loss: 0.009733 Vali Loss: 0.006114 LR: 0.000197

2025-05-20 18:31:05,374 - New best validation loss: 0.006114. Saving checkpoint...
2025-05-20 18:31:05,552 - gcc -pthread -B /home/<USER>/anaconda3/envs/SimVP/compiler_compat -fno-strict-overflow -Wsign-compare -DNDEBUG -O2 -Wall -fPIC -O2 -isystem /home/<USER>/anaconda3/envs/SimVP/include -fPIC -O2 -isystem /home/<USER>/anaconda3/envs/SimVP/include -fPIC -c /tmp/tmpkfp61w67/test.c -o /tmp/tmpkfp61w67/test.o
2025-05-20 18:31:05,589 - gcc -pthread -B /home/<USER>/anaconda3/envs/SimVP/compiler_compat /tmp/tmpkfp61w67/test.o -laio -o /tmp/tmpkfp61w67/a.out
2025-05-20 18:31:06,513 - gcc -pthread -B /home/<USER>/anaconda3/envs/SimVP/compiler_compat -fno-strict-overflow -Wsign-compare -DNDEBUG -O2 -Wall -fPIC -O2 -isystem /home/<USER>/anaconda3/envs/SimVP/include -fPIC -O2 -isystem /home/<USER>/anaconda3/envs/SimVP/include -fPIC -c /tmp/tmppvfg582e/test.c -o /tmp/tmppvfg582e/test.o
2025-05-20 18:31:06,551 - gcc -pthread -B /home/<USER>/anaconda3/envs/SimVP/compiler_compat /tmp/tmppvfg582e/test.o -L/usr/local/cuda-11.8 -L/usr/local/cuda-11.8/lib64 -lcufile -o /tmp/tmppvfg582e/a.out
2025-05-20 18:31:06,659 - gcc -pthread -B /home/<USER>/anaconda3/envs/SimVP/compiler_compat -fno-strict-overflow -Wsign-compare -DNDEBUG -O2 -Wall -fPIC -O2 -isystem /home/<USER>/anaconda3/envs/SimVP/include -fPIC -O2 -isystem /home/<USER>/anaconda3/envs/SimVP/include -fPIC -c /tmp/tmp4xti29j_/test.c -o /tmp/tmp4xti29j_/test.o
2025-05-20 18:31:06,697 - gcc -pthread -B /home/<USER>/anaconda3/envs/SimVP/compiler_compat /tmp/tmp4xti29j_/test.o -laio -o /tmp/tmp4xti29j_/a.out
2025-05-20 18:31:08,147 - 检查点已保存到: ./520/UNet_hmse_lion_128/checkpoints/checkpoint.pth
2025-05-20 18:43:02,184 - Epoch: 2 | Train Loss: 0.005848 Vali Loss: 0.005779 LR: 0.000394

2025-05-20 18:43:02,185 - New best validation loss: 0.005779. Saving checkpoint...
2025-05-20 18:43:02,224 - 检查点已保存到: ./520/UNet_hmse_lion_128/checkpoints/checkpoint.pth
2025-05-20 18:55:06,402 - Epoch: 3 | Train Loss: 0.005324 Vali Loss: 0.005013 LR: 0.000591

2025-05-20 18:55:06,402 - New best validation loss: 0.005013. Saving checkpoint...
2025-05-20 18:55:06,440 - 检查点已保存到: ./520/UNet_hmse_lion_128/checkpoints/checkpoint.pth
2025-05-20 19:07:34,917 - Epoch: 4 | Train Loss: 0.004900 Vali Loss: 0.004799 LR: 0.000788

2025-05-20 19:07:34,918 - New best validation loss: 0.004799. Saving checkpoint...
2025-05-20 19:07:34,949 - 检查点已保存到: ./520/UNet_hmse_lion_128/checkpoints/checkpoint.pth
2025-05-20 19:19:25,675 - Epoch: 5 | Train Loss: nan Vali Loss: nan LR: 0.000872

2025-05-20 19:31:43,005 - Epoch: 6 | Train Loss: nan Vali Loss: nan LR: 0.000875

2025-05-20 19:43:24,590 - Epoch: 7 | Train Loss: nan Vali Loss: nan LR: 0.000879

2025-05-20 19:55:32,273 - Epoch: 8 | Train Loss: nan Vali Loss: nan LR: 0.000881

2025-05-20 20:07:28,460 - Epoch: 9 | Train Loss: nan Vali Loss: nan LR: 0.000881

2025-05-20 20:25:01,826 - --- Detailed Validation Metrics for Epoch 10 ---
2025-05-20 20:25:01,826 -   vali mse_denorm: nan
2025-05-20 20:25:01,826 -   vali mae_denorm: nan
2025-05-20 20:25:01,826 -   vali csi_10: 0.0643
2025-05-20 20:25:01,826 -   vali pod_10: 0.9955
2025-05-20 20:25:01,826 -   vali far_10: 0.9357
2025-05-20 20:25:01,826 -   vali hss_10: 0.0160
2025-05-20 20:25:01,826 -   vali csi_20: 0.0305
2025-05-20 20:25:01,826 -   vali pod_20: 0.9871
2025-05-20 20:25:01,826 -   vali far_20: 0.9695
2025-05-20 20:25:01,826 -   vali hss_20: 0.0178
2025-05-20 20:25:01,826 -   vali csi_30: 0.0197
2025-05-20 20:25:01,826 -   vali pod_30: 0.9494
2025-05-20 20:25:01,826 -   vali far_30: 0.9803
2025-05-20 20:25:01,826 -   vali hss_30: 0.0268
2025-05-20 20:25:01,826 -   vali csi_40: 0.0063
2025-05-20 20:25:01,827 -   vali pod_40: 0.8555
2025-05-20 20:25:01,827 -   vali far_40: 0.9937
2025-05-20 20:25:01,827 -   vali hss_40: 0.0109
2025-05-20 20:25:01,827 - --- End Detailed Validation Metrics for Epoch 10 ---
2025-05-20 20:25:01,827 - Epoch: 10 | Train Loss: nan Vali Loss: nan LR: 0.000881

2025-05-20 20:36:52,229 - Epoch: 11 | Train Loss: nan Vali Loss: nan LR: 0.000881

2025-05-20 20:48:40,025 - Epoch: 12 | Train Loss: nan Vali Loss: nan LR: 0.000881

2025-05-20 21:00:28,279 - Epoch: 13 | Train Loss: nan Vali Loss: nan LR: 0.000882

