#!/usr/bin/env python3
"""
测试纯频域小波融合的VCBNet2
"""

import torch
import torch.nn as nn

def test_pure_freq_wtconv():
    """测试纯频域小波模块"""
    print("🧪 测试PureFreqWTConv2d模块")
    
    try:
        from pure_wtconv import PureFreqWTConv2d
        
        # 测试不同通道配置
        configs = [
            (32, 16),  # 32 -> 16
            (64, 32),  # 64 -> 32
            (32, 32),  # 32 -> 32 (相同通道)
        ]
        
        for in_ch, out_ch in configs:
            print(f"\n🔍 测试配置: {in_ch} -> {out_ch}")
            
            module = PureFreqWTConv2d(
                in_channels=in_ch, 
                out_channels=out_ch,
                wt_levels=2,
                wt_type='db4'
            )
            module.eval()
            
            # 测试输入
            test_input = torch.randn(1, in_ch, 128, 256)
            
            with torch.no_grad():
                output = module(test_input)
            
            print(f"✅ 输入: {test_input.shape}")
            print(f"✅ 输出: {output.shape}")
            print(f"✅ 通道变换: {in_ch} -> {out_ch} ✓")
            
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False

def test_spatial_freq_fusion():
    """测试空间-频域融合模块"""
    print("\n🧪 测试SpatialFreqFusionOutConv模块")
    
    try:
        from vcbnet2 import SpatialFreqFusionOutConv
        
        module = SpatialFreqFusionOutConv(in_channels=32, out_channels=1)
        module.eval()
        
        test_input = torch.randn(2, 32, 128, 256)
        
        with torch.no_grad():
            output = module(test_input)
        
        print(f"✅ 输入: {test_input.shape}")
        print(f"✅ 输出: {output.shape}")
        
        # 检查是否使用小波
        if hasattr(module, 'use_wavelet') and module.use_wavelet:
            print("✅ 使用纯频域小波变换")
        else:
            print("⚠️  使用常规卷积替代")
            
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_vcbnet2():
    """测试完整VCBNet2"""
    print("\n🚀 测试VCBNet2模型")
    
    try:
        from vcbnet2 import VCBNet2
        
        model = VCBNet2(n_channels=3, n_outputs=1)
        model.eval()
        
        # 参数统计
        total_params = sum(p.numel() for p in model.parameters())
        print(f"📊 总参数量: {total_params:,}")
        
        # 测试前向传播
        test_input = torch.randn(1, 3, 256, 512)
        
        with torch.no_grad():
            output = model(test_input)
        
        print(f"✅ 输入: {test_input.shape}")
        print(f"✅ 输出: {output.shape}")
        print(f"✅ 输出范围: [{output.min().item():.4f}, {output.max().item():.4f}]")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def compare_fusion_methods():
    """对比不同融合方法"""
    print("\n🔄 对比融合方法")
    
    try:
        from vcbnet2 import RegressionOutConv, SpatialFreqFusionOutConv
        
        # 创建模块
        original = RegressionOutConv(32, 1)
        enhanced = SpatialFreqFusionOutConv(32, 1)
        
        # 参数对比
        orig_params = sum(p.numel() for p in original.parameters())
        enh_params = sum(p.numel() for p in enhanced.parameters())
        
        print(f"📊 参数对比:")
        print(f"   原始模块: {orig_params:,}")
        print(f"   增强模块: {enh_params:,}")
        print(f"   增加倍数: {enh_params/orig_params:.2f}x")
        
        # 输出对比
        test_input = torch.randn(1, 32, 128, 256)
        
        with torch.no_grad():
            orig_out = original(test_input)
            enh_out = enhanced(test_input)
        
        print(f"\n📋 输出对比:")
        print(f"   原始输出: {orig_out.shape}, 范围: [{orig_out.min():.4f}, {orig_out.max():.4f}]")
        print(f"   增强输出: {enh_out.shape}, 范围: [{enh_out.min():.4f}, {enh_out.max():.4f}]")
        
        return True
        
    except Exception as e:
        print(f"❌ 对比失败: {str(e)}")
        return False

if __name__ == "__main__":
    print("🌊 纯频域小波融合测试")
    print("=" * 50)
    
    tests = [
        test_pure_freq_wtconv,
        test_spatial_freq_fusion, 
        test_vcbnet2,
        compare_fusion_methods
    ]
    
    results = []
    for test_func in tests:
        try:
            result = test_func()
            results.append(result)
        except Exception as e:
            print(f"❌ {test_func.__name__} 失败: {str(e)}")
            results.append(False)
    
    print("\n" + "=" * 50)
    print("📋 测试总结:")
    success = sum(results)
    total = len(results)
    print(f"   成功: {success}/{total}")
    
    if success == total:
        print("🎉 所有测试通过！")
        print("\n💡 设计要点:")
        print("   ✅ 空间分支: CBR(32->16)")
        print("   ✅ 频域分支: PureFreqWT(32->16)")  
        print("   ✅ 融合方式: Element-wise Add")
        print("   ✅ 最终输出: Conv1x1(16->1)")
    else:
        print("⚠️  部分测试失败")
