#!/usr/bin/env python3
"""
测试修改后的VCBNet（使用FreqFusion在输出层融合）
"""

import torch
import torch.nn as nn
from vcbnet import VCBNet

def test_vcbnet_with_freqfusion():
    """测试修改后的VCBNet"""
    print("🧪 测试修改后的VCBNet（输出层FreqFusion融合）")
    
    # 创建模型
    model = VCBNet(n_channels=3, n_outputs=1)
    model.eval()
    
    # 打印模型结构信息
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    print(f"📊 总参数量: {total_params:,}")
    print(f"📊 可训练参数量: {trainable_params:,}")
    
    # 测试不同输入尺寸
    test_sizes = [
        (1, 3, 256, 512),   # 小尺寸测试
        (1, 3, 512, 1024),  # 中等尺寸
        (1, 3, 768, 1536),  # 实际使用尺寸
    ]
    
    for batch_size, channels, height, width in test_sizes:
        print(f"\n🔍 测试输入尺寸: {(batch_size, channels, height, width)}")
        
        # 创建测试输入
        test_input = torch.randn(batch_size, channels, height, width)
        
        try:
            # 前向传播测试
            with torch.no_grad():
                output = model(test_input)
            
            print(f"✅ 输出形状: {output.shape}")
            print(f"✅ 输出数据类型: {output.dtype}")
            print(f"✅ 输出值范围: [{output.min().item():.4f}, {output.max().item():.4f}]")
            
            # 检查输出是否包含NaN或Inf
            if torch.isnan(output).any():
                print("⚠️  警告: 输出包含NaN值")
            if torch.isinf(output).any():
                print("⚠️  警告: 输出包含Inf值")
                
        except Exception as e:
            print(f"❌ 错误: {str(e)}")
            return False
    
    print("\n✅ 所有测试通过！")
    return True

def analyze_model_components():
    """分析模型各组件"""
    print("\n🔍 分析模型组件")
    
    model = VCBNet(n_channels=3, n_outputs=1)
    
    # 检查关键组件
    print(f"📋 VAN分支输出层: {model.cls_reg}")
    print(f"📋 U-Net分支输出层: {model.out}")
    print(f"📋 FreqFusion模块: {model.freq_fusion}")
    print(f"📋 最终卷积层: {model.final_conv}")
    
    # 分析FreqFusion参数
    freq_fusion_params = sum(p.numel() for p in model.freq_fusion.parameters())
    print(f"📊 FreqFusion参数量: {freq_fusion_params:,}")

def compare_with_original():
    """与原始VCBNet进行对比"""
    print("\n🔄 对比分析")
    
    # 这里可以加载原始VCBNet进行对比
    # 由于原始代码已被修改，这里只是展示对比的思路
    print("💡 建议对比项目:")
    print("   1. 参数量变化")
    print("   2. 计算复杂度")
    print("   3. 内存使用")
    print("   4. 推理速度")
    print("   5. 训练稳定性")

if __name__ == "__main__":
    print("🚀 开始测试修改后的VCBNet")
    
    # 运行测试
    success = test_vcbnet_with_freqfusion()
    
    if success:
        # 分析模型组件
        analyze_model_components()
        
        # 对比分析
        compare_with_original()
        
        print("\n🎉 测试完成！")
        print("\n💡 下一步建议:")
        print("   1. 在小数据集上快速验证训练效果")
        print("   2. 对比原始VCBNet的性能")
        print("   3. 调整FreqFusion的超参数")
        print("   4. 考虑添加残差连接或注意力机制")
    else:
        print("\n❌ 测试失败，请检查模型实现")
