import os
import glob
import random

def collect_files_for_year(data_root, year):
    """收集指定年份下的所有npz文件"""
    all_files = []
    year_path = os.path.join(data_root, str(year))
    if not os.path.isdir(year_path):
        return []
    
    found_in_year = False
    for month_num in range(1, 13):
        month_str = f"{month_num:02d}"
        month_path = os.path.join(year_path, month_str)
        if os.path.isdir(month_path):
            pattern = os.path.join(month_path, "*.npz")
            month_files = glob.glob(pattern)
            if month_files:
                all_files.extend(month_files)
                found_in_year = True
    
    if found_in_year:
        all_files.sort()
    return all_files

def generate_datalist(data_root, output_dir, year=2020, train_split_ratio=0.8, random_seed=42):
    """生成训练集和验证集的文件列表"""
    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)
    
    # 收集文件
    all_files = collect_files_for_year(data_root, year)
    if not all_files:
        raise FileNotFoundError(f"在{data_root}目录下未找到{year}年的npz文件。")
    
    # 设置随机种子并打乱文件列表
    random.seed(random_seed)
    random.shuffle(all_files)
    
    # 划分训练集和验证集
    split_idx = int(len(all_files) * train_split_ratio)
    train_files = all_files[:split_idx]
    val_files = all_files[split_idx:]
    
    # 保存文件列表
    train_list_path = os.path.join(output_dir, f"us{year}_train.txt")
    val_list_path = os.path.join(output_dir, f"us{year}_val.txt")
    
    # 写入训练集文件列表
    with open(train_list_path, 'w') as f:
        for file_path in train_files:
            f.write(f"{file_path}\n")
    
    # 写入验证集文件列表
    with open(val_list_path, 'w') as f:
        for file_path in val_files:
            f.write(f"{file_path}\n")
    
    print(f"生成的文件列表信息：")
    print(f"训练集文件数量: {len(train_files)}")
    print(f"验证集文件数量: {len(val_files)}")
    print(f"文件列表已保存到：{output_dir}")

if __name__ == '__main__':
    # 设置参数
    data_root = '/Storage01/ShareData/radar_us/STR_US/counts/'
    output_dir = '../datalist/us2020/'
    year = 2020
    train_split_ratio = 0.8
    random_seed = 42
    
    # 生成文件列表
    generate_datalist(
        data_root=data_root,
        output_dir=output_dir,
        year=year,
        train_split_ratio=train_split_ratio,
        random_seed=random_seed
    )