import re
import matplotlib.pyplot as plt
import numpy as np

def parse_log_file(log_file):
    epochs = []
    train_losses = []
    val_losses = []
    metrics = {}
    
    # 初始化指标字典
    for threshold in [10, 20, 30, 40]:
        for metric in ['pod', 'csi', 'hss', 'far']:
            metrics[f'{metric}_{threshold}'] = []
    
    with open(log_file, 'r', encoding='utf-8') as f:
        lines = f.readlines()
        
    current_epoch = None
    for line in lines:
        # 解析训练和验证损失
        epoch_match = re.search(r'Epoch: (\d+) \| Train Loss: ([\d.]+) Vali Loss: ([\d.]+)', line)
        if epoch_match:
            epochs.append(int(epoch_match.group(1)))
            train_losses.append(float(epoch_match.group(2)))
            val_losses.append(float(epoch_match.group(3)))
        
        # 解析详细指标
        for threshold in [10, 20, 30, 40]:
            for metric in ['pod', 'csi', 'hss', 'far']:
                metric_match = re.search(f'vali {metric}_{threshold}: ([\d.]+)', line)
                if metric_match:
                    metrics[f'{metric}_{threshold}'].append(float(metric_match.group(1)))
    
    return epochs, train_losses, val_losses, metrics

def plot_metrics(log_file):
    epochs, train_losses, val_losses, metrics = parse_log_file(log_file)
    
    # 创建图形和子图
    fig = plt.figure(figsize=(15, 10))
    gs = plt.GridSpec(2, 2, width_ratios=[1, 1], height_ratios=[1, 1])
    
    # 损失曲线图
    ax_loss = plt.subplot(gs[0, :])
    ax_loss.plot(epochs, train_losses, label='Train Loss', marker='o', markersize=2)
    ax_loss.plot(epochs, val_losses, label='Validation Loss', marker='o', markersize=2)
    ax_loss.set_title('训练和验证损失曲线')
    ax_loss.set_xlabel('Epoch')
    ax_loss.set_ylabel('Loss')
    ax_loss.legend()
    ax_loss.grid(True)
    
    # 为四个阈值创建子图
    thresholds = [10, 20, 30, 40]
    positions = [(1, 0), (1, 1)]
    
    for i, threshold in enumerate(thresholds):
        row = (i // 2) + 1
        col = i % 2
        ax = plt.subplot(gs[positions[col]])
        
        # 绘制该阈值下的所有指标
        for metric in ['pod', 'csi', 'hss', 'far']:
            key = f'{metric}_{threshold}'
            if metrics[key]:  # 确保有数据再绘制
                ax.plot(range(len(metrics[key])), metrics[key], 
                        label=metric.upper(), marker='o', markersize=2)
        
        ax.set_title(f'阈值 {threshold} 的评估指标')
        ax.set_xlabel('评估次数')
        ax.set_ylabel('指标值')
        ax.legend()
        ax.grid(True)
    
    plt.tight_layout()
    plt.savefig('metrics_plot.png', dpi=300, bbox_inches='tight')
    plt.close()

if __name__ == '__main__':
    log_file = 'logs/log.log'
    plot_metrics(log_file)