import argparse
from exp import Exp
import warnings
warnings.filterwarnings('ignore')

def create_parser():
    parser = argparse.ArgumentParser()
    # 基本参数
    parser.add_argument('--res_dir', default='./514_results_improved', type=str)
    parser.add_argument('--ex_name', default='Debug', type=str)
    parser.add_argument('--seed', default=42, type=int)
    
    # 数据参数
    parser.add_argument('--batch_size', default=2, type=int)
    parser.add_argument('--val_batch_size', default=1, type=int)
    parser.add_argument('--data_root', default='/Storage01/ShareData/radar_us/STR_US/counts/')
    parser.add_argument('--dataname', default='mmnist', choices=['mmnist', 'taxibj'])
    parser.add_argument('--num_workers', default=8, type=int)
    parser.add_argument('--in_shape', default=[4, 768, 1536], type=int, nargs='*')
    
    # UNet 模型参数
    parser.add_argument('--out_chans', default=1, type=int)
    parser.add_argument('--unet_channels', default=[32, 64, 128, 256], type=int, nargs='*')
    parser.add_argument('--unet_skip', default=True, type=bool)
    
    # 训练参数
    parser.add_argument('--epochs', default=51, type=int)
    parser.add_argument('--log_step', default=1, type=int)
    parser.add_argument('--lr', default=0.001, type=float)
    parser.add_argument('--scheduler', default='cosine', type=str, choices=['constant', 'linear', 'cosine'])
    parser.add_argument('--warmup_steps', default=50, type=int)
    
    # 混合精度训练参数
    parser.add_argument('--mixed_precision', type=str, default='fp16', choices=['no', 'fp16', 'bf16'])
    
    # 分布式训练参数
    parser.add_argument('--gradient_accumulation_steps', type=int, default=1)
    parser.add_argument('--gradient_clipping', type=float, default=1.0)
    
    return parser

if __name__ == '__main__':
    args = create_parser().parse_args()
    exp = Exp(args)
    print('>>>>>>>>>>>>>>>>>>>>>>>>>>>>  开始训练  <<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<')
    exp.train(args)
    print('>>>>>>>>>>>>>>>>>>>>>>>>>>>> 开始测试 <<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<')
    mse = exp.test(args)