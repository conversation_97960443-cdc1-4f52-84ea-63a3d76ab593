import os
#os.environ["CUDA_VISIBLE_DEVICES"] = "0"
import os.path as osp
import json
import torch
# import pickle #不再需要，因为 accelerator.save/load 处理字典
import logging
import numpy as np
from tqdm import tqdm
from accelerate import Accelerator, DistributedDataParallelKwargs
from accelerate.utils import set_seed, ProjectConfiguration
from model import UNet
from utils import *
#from API import dataloader.load_data
from API import *

from diffusers import (
    get_constant_schedule_with_warmup,
    get_linear_schedule_with_warmup,
    get_cosine_schedule_with_warmup,
)

# 脚本开始时尝试清空CUDA缓存，可能有助于在某些情况下减少显存碎片
torch.cuda.empty_cache # 保持原样
class Exp:
    def __init__(self, args):
        super(Exp, self).__init__()
        self.args = args
        self.config = self.args.__dict__
        set_seed(self.args.seed)
        self.path = osp.join(self.args.res_dir, self.args.ex_name)
        check_dir(self.path)

        self.checkpoints_path = osp.join(self.path, 'checkpoints')
        check_dir(self.checkpoints_path)

        self.log_path = osp.join(self.path, 'logs')
        check_dir(self.log_path)

        self.best_val_loss = float('inf')

        ddp_plugin = DistributedDataParallelKwargs(find_unused_parameters=False)
        project_config = ProjectConfiguration(project_dir=self.args.res_dir,
                                              logging_dir=self.log_path)

        self.accelerator = Accelerator(
            split_batches=True,
            mixed_precision=self.args.mixed_precision if hasattr(args, 'mixed_precision') else 'no',
            project_config=project_config,
            gradient_accumulation_steps=self.args.gradient_accumulation_steps,
            kwargs_handlers=[ddp_plugin]
        )
        self.device = self.accelerator.device
        self._preparation()
        if self.accelerator.is_main_process:
            print_log(output_namespace(self.args))
            print_log(f"Using mixed precision: {self.args.mixed_precision if hasattr(args, 'mixed_precision') else 'no'}")
            print_log(f"Number of processes: {self.accelerator.num_processes}")
            print_log(f"Distributed type: {self.accelerator.distributed_type}")

        self._get_data()
        self._build_model()
        self._select_optimizer()
        self._select_criterion()

        self.model, self.optimizer, self.scheduler, self.train_loader, self.vali_loader, self.test_loader = self.accelerator.prepare(
            self.model, self.optimizer, self.scheduler, self.train_loader, self.vali_loader, self.test_loader
        )

    def _preparation(self):
        sv_param = osp.join(self.path, 'model_param.json')
        if self.accelerator.is_main_process:
            with open(sv_param, 'w') as file_obj:
                json.dump(self.args.__dict__, file_obj)

            for handler in logging.root.handlers[:]:
                logging.root.removeHandler(handler)
            logging.basicConfig(level=logging.INFO, filename=osp.join(self.log_path, 'log.log'),
                                filemode='a', format='%(asctime)s - %(message)s')

    def _build_model(self):
        args = self.args
        self.model = UNet(in_chans=args.in_shape[0],
                          out_chans=args.out_chans,
                          channels=args.unet_channels,
                          skip=args.unet_skip)

    def _get_data(self):
        config = self.args.__dict__
        self.train_loader, self.vali_loader, self.test_loader, self.data_max, self.data_min = load_data(**config)
        self.vali_loader = self.test_loader if self.vali_loader is None else self.vali_loader
        self.test_loader = self.vali_loader if self.test_loader is None else self.test_loader

    def _select_optimizer(self):
        self.optimizer = torch.optim.Adam(self.model.parameters(), lr=self.args.lr)

        num_steps_per_epoch = len(self.train_loader)
        total_steps = (num_steps_per_epoch // self.args.gradient_accumulation_steps) * self.args.epochs
        warmup_steps = self.args.warmup_steps

        if self.args.scheduler == 'constant':
            self.scheduler = get_constant_schedule_with_warmup(
                self.optimizer,
                num_warmup_steps=warmup_steps,
            )
        elif self.args.scheduler == 'linear':
            self.scheduler = get_linear_schedule_with_warmup(
                self.optimizer,
                num_warmup_steps=warmup_steps,
                num_training_steps=total_steps,
            )
        elif self.args.scheduler == 'cosine':
            self.scheduler = get_cosine_schedule_with_warmup(
                self.optimizer,
                num_warmup_steps=warmup_steps,
                num_training_steps=total_steps,
            )
        else:
            raise ValueError(f"Invalid scheduler_type. Expected 'constant', 'linear', or 'cosine', got: {self.args.scheduler}")

    def _select_criterion(self):
        self.criterion = torch.nn.MSELoss()

    def _save(self, name=''):
        if not self.accelerator.is_main_process:
            return
        unwrapped_model = self.accelerator.unwrap_model(self.model)
        checkpoint = {
            'model': unwrapped_model.state_dict(), # 保存 unwrapped 模型权重，确保可移植性
            'optimizer': self.optimizer.state_dict(),
            'scheduler': self.scheduler.state_dict(),
        }
        self.accelerator.save(checkpoint, osp.join(self.checkpoints_path, name + '.pth'))
        if self.accelerator.is_main_process: # 添加日志
            print_log(f"检查点已保存到: {osp.join(self.checkpoints_path, name + '.pth')}")

    def _load(self, path):
        if not os.path.exists(path):
            if self.accelerator.is_main_process:
                print_log(f"Warning: 模型文件 {path} 不存在，跳过加载")
            return False

        try:
            # Accelerator 的 load 方法不直接用于加载这种自定义字典，所以仍用 torch.load
            # 但因为保存的是 unwrapped_model.state_dict()，加载逻辑也需要对应
            if self.accelerator.is_main_process: print_log(f"尝试从 {path} 加载检查点...")
            checkpoint = torch.load(path, map_location=self.device)

            unwrapped_model = self.accelerator.unwrap_model(self.model)
            unwrapped_model.load_state_dict(checkpoint['model'])
            self.model = self.accelerator.prepare(unwrapped_model) # 不需要再次 prepare 模型本身，因为权重已更新

            self.optimizer.load_state_dict(checkpoint['optimizer'])
            self.scheduler.load_state_dict(checkpoint['scheduler'])

            if self.accelerator.is_main_process:
                print_log(f"从 {path} 加载检查点成功。")
            return True
        except Exception as e:
            if self.accelerator.is_main_process:
                print_log(f"加载检查点 {path} 失败: {e}。")
            return False

    def train(self, args):
        config = args.__dict__

        self.model.train()  # 设置模型为训练模式
        for epoch in range(config['epochs']):
            train_loss = []
            train_pbar = tqdm(self.train_loader, disable=not self.accelerator.is_main_process)

            for batch_x, batch_y in train_pbar:
                self.optimizer.zero_grad()

                with self.accelerator.autocast(): # 明确使用 autocast 进行混合精度
                    pred_y = self.model(batch_x)
                    loss = self.criterion(pred_y, batch_y)

                self.accelerator.backward(loss)

                self.accelerator.wait_for_everyone()
                if self.accelerator.sync_gradients:
                    clip_value = self.args.gradient_clipping if hasattr(self.args, 'gradient_clipping') else 1.0
                    if clip_value > 0:
                        self.accelerator.clip_grad_norm_(self.model.parameters(), clip_value)

                self.optimizer.step()
                if not self.accelerator.optimizer_step_was_skipped:
                    self.scheduler.step()

                train_loss.append(loss.item())
                current_lr = self.optimizer.param_groups[0]['lr']
                train_pbar.set_description('train loss: {:.4f}, lr: {:.6f}'.format(loss.item(), current_lr))

            local_train_loss = np.average(train_loss) if train_loss else 0.0
            gathered_train_loss = self.accelerator.gather(torch.tensor(local_train_loss, device=self.device)).mean().item()

            if epoch % args.log_step == 0:
                with torch.no_grad(): # 验证时不需要计算梯度
                    vali_loss = self.vali(self.vali_loader)
                    if self.accelerator.is_main_process:
                        if epoch > 0 and epoch % (args.log_step * 100) == 0:
                            self._save(name=str(epoch))
                        current_lr = self.optimizer.param_groups[0]['lr']
                        print_log("Epoch: {0} | Train Loss: {1:.4f} Vali Loss: {2:.4f} LR: {3:.6f}\n".format(
                            epoch + 1, gathered_train_loss, vali_loss, current_lr))
                        # 检查当前验证损失是否优于已记录的最佳损失
                        if vali_loss < self.best_val_loss:
                            self.best_val_loss = vali_loss # 更新最佳损失
                            print_log(f"New best validation loss: {self.best_val_loss:.6f}. Saving checkpoint...")
                            self._save(name='checkpoint')

        self.accelerator.wait_for_everyone()
        best_model_path = osp.join(self.checkpoints_path, 'checkpoint.pth')
        if self.accelerator.is_main_process:
            print_log(f"加载最佳模型: {best_model_path}")

        if osp.exists(best_model_path):
            self._load(best_model_path)
        else:
            if self.accelerator.is_main_process:
                print_log(f"Warning: 最佳模型文件 {best_model_path} 未找到。将使用最后一个epoch的模型进行测试。")

        self.accelerator.wait_for_everyone()

    @torch.no_grad()
    def test(self, args):
        self.model.eval()
        test_pbar = tqdm(self.test_loader, disable=not self.accelerator.is_main_process)
        all_batch_test_losses = []
        # 初始化用于累积指标的变量
        mse_total, mae_total = 0.0, 0.0
        num_samples = 0 # 记录总样本数

        for batch_x, batch_y in test_pbar: # 遍历测试数据
            # 使用 accelerate.autocast() 进行混合精度推理
            with self.accelerator.autocast():
                pred_y = self.model(batch_x) # 模型预测
            gathered_pred = self.accelerator.gather(pred_y)
            gathered_batch_y = self.accelerator.gather(batch_y)

            if self.accelerator.is_main_process:
                pred_np = gathered_pred.detach().cpu().numpy()
                batch_y_np = gathered_batch_y.detach().cpu().numpy()

                mse, mae = metric(pred_np, batch_y_np,
                                  self.data_max, # 使用半角逗号
                                  self.data_min) # 使用半角逗号

                actual_batch_size = pred_np.shape[0] # 获取实际的全局批次大小
                # 累积指标 (乘以批次大小，后续计算加权平均)
                mse_total += mse * actual_batch_size
                mae_total += mae * actual_batch_size
                num_samples += actual_batch_size

        # 仅主进程计算并打印最终的平均指标
        if self.accelerator.is_main_process and num_samples > 0:
            mse_avg = mse_total / num_samples
            mae_avg = mae_total / num_samples
            print_log('Test metrics:')
            print_log('mse:{:.4f}, mae:{:.4f}'.format(
                mse_avg, mae_avg))
            return mse_avg # 返回平均MSE

        return 0.0 # 如果不是主进程或没有样本，返回0

    @torch.no_grad()
    def vali(self, vali_loader):
        self.model.eval()
        total_loss = []
        vali_pbar = tqdm(vali_loader, disable=not self.accelerator.is_main_process)

        mse_total, mae_total = 0.0, 0.0
        num_samples = 0

        for i, (batch_x, batch_y) in enumerate(vali_pbar): # 遍历验证数据
            if i * batch_x.shape[0] > 1000: # batch_x.shape[0] 是当前进程的批次大小
                break

            with self.accelerator.autocast():
                pred_y = self.model(batch_x) # 模型预测

            loss = self.criterion(pred_y, batch_y) # 计算损失 (标量)
            total_loss.append(loss.item())
            vali_pbar.set_description('vali loss: {:.4f}'.format(loss.item()))

            # 收集所有进程的预测结果和真实标签
            gathered_pred = self.accelerator.gather(pred_y)
            gathered_batch_y = self.accelerator.gather(batch_y)

            if self.accelerator.is_main_process:
                pred_np = gathered_pred.detach().cpu().numpy()
                batch_y_np = gathered_batch_y.detach().cpu().numpy()

                mse, mae = metric(pred_np, batch_y_np,
                                  self.data_max, # 使用半角逗号
                                  self.data_min) # 使用半角逗号

                actual_batch_size = pred_np.shape[0]
                mse_total += mse * actual_batch_size
                mae_total += mae * actual_batch_size
                num_samples += actual_batch_size

        local_total_loss = np.average(total_loss) if total_loss else 0.0
        gathered_total_loss = self.accelerator.gather(torch.tensor(local_total_loss, device=self.device)).mean().item()

        if self.accelerator.is_main_process and num_samples > 0:
            mse_avg = mse_total / num_samples
            mae_avg = mae_total / num_samples
            print_log('vali mse:{:.4f}, mae:{:.4f}'.format(
                mse_avg, mae_avg))

        self.model.train()  # 验证结束后，恢复模型为训练模式
        return gathered_total_loss # 返回全局平均验证损失