import os
import numpy as np
import torch
from torch.utils.data import Dataset, DataLoader
import glob
import random

# --- Gremlin CLASS (Modified __init__ only, rest is your original logic) ---
class Gremlin(Dataset):
    def __init__(self, file_list, transform=None):
        self.transform = transform
        self.samples = file_list
        if not self.samples:
            # This allows an "empty" dataset if no files are provided
            self.xshape = None 
            self.tshape = None
            return # Important to return if samples is empty, to avoid error in _get_shape
        
        self.samples.sort()
        # _get_shape is called here. It ASSUMES:
        # 1. self.samples[0] exists and is a valid .npz file.
        # 2. Inside that .npz: data['xdata'] is (H, W, C_in)
        # 3. Inside that .npz: data['ydata'] is (H, W)
        # If these assumptions are wrong for the first file, this will error.
        try:
            self.xshape, self.tshape = self._get_shape()
        except Exception as e: # General exception for issues in _get_shape
            first_sample = self.samples[0] # samples is guaranteed not empty here
            raise RuntimeError(f"Error in _get_shape() for first sample '{first_sample}': {e}. Check .npz content and Gremlin._get_shape assumptions.")

    def _get_shape(self):
        # UNCHANGED FROM YOUR ORIGINAL
        with np.load(self.samples[0]) as data:
            x_np = data['xdata']
            t_np = data['ydata']
            xshape = np.moveaxis(x_np, -1, 0).shape
            tshape = np.expand_dims(t_np, axis=0).shape
        return xshape, tshape

    def __len__(self):
        # UNCHANGED FROM YOUR ORIGINAL
        return len(self.samples)

    def __getitem__(self, idx):
        # UNCHANGED FROM YOUR ORIGINAL (except no .copy())
        f = self.samples[idx]
        try:
            with np.load(f) as data:
                x_np = data['xdata'] # ASSUMED (H, W, C_in)
                t_np = data['ydata'] # ASSUMED (H, W)
        except Exception as e:
            raise IOError(f"Error loading or processing {f}: {e}")

        x_np_transformed = np.moveaxis(x_np, -1, 0)
        t_np_transformed = np.expand_dims(t_np, axis=0)

        x = torch.from_numpy(x_np_transformed).float()
        t = torch.from_numpy(t_np_transformed).float()

        if self.transform is not None:
            x = self.transform(x)
            t = self.transform(t)
        return x, t
# --- END OF MODIFIED Gremlin CLASS ---


def _collect_files_for_year(data_root, year):
    # (No changes, no prints)
    all_files = []
    year_path = os.path.join(data_root, str(year))
    if not os.path.isdir(year_path):
        return []
    found_in_year = False
    for month_num in range(1, 13):
        month_str = f"{month_num:02d}"
        month_path = os.path.join(year_path, month_str)
        if os.path.isdir(month_path):
            pattern = os.path.join(month_path, "*.npz")
            month_files = glob.glob(pattern)
            if month_files:
                all_files.extend(month_files)
                found_in_year = True
    if found_in_year:
        all_files.sort()
    return all_files

def load_data(batch_size, val_batch_size, data_root, num_workers, 
              **kwargs):
    data_year=2020
    train_split_ratio=0.8
    random_seed=42
    all_files_for_year = _collect_files_for_year(data_root, data_year)
    if not all_files_for_year:
        # This FileNotFoundError is acceptable as it indicates a fundamental issue
        raise FileNotFoundError(f"No .npz files found for year {data_year} in {data_root}.")

    random.seed(random_seed)
    random.shuffle(all_files_for_year)

    split_idx = int(len(all_files_for_year) * train_split_ratio)
    train_files_paths = all_files_for_year[:split_idx]
    val_files_paths = all_files_for_year[split_idx:]

    transform = None 

    train_dataset = None
    if train_files_paths: 
        train_dataset = Gremlin(file_list=train_files_paths, transform=transform)
        if train_dataset.xshape is None: # Handles if Gremlin init results in empty dataset
            train_dataset = None 
    
    val_dataset = None
    if val_files_paths: 
        val_dataset = Gremlin(file_list=val_files_paths, transform=transform)
        if val_dataset.xshape is None:
            val_dataset = None

    dataloader_train = None
    # Check if dataset exists AND has samples before creating DataLoader
    if train_dataset and len(train_dataset) > 0: 
        dataloader_train = DataLoader(
            train_dataset, batch_size=batch_size, shuffle=True,
            num_workers=num_workers, pin_memory=True
        )
    
    dataloader_validation = None
    if val_dataset and len(val_dataset) > 0:
        dataloader_validation = DataLoader(
            val_dataset, batch_size=val_batch_size, shuffle=False,
            num_workers=num_workers, pin_memory=True
        )
    
    dataloader_test = None

    max = 60
    min = 0   

    return dataloader_train, dataloader_validation, dataloader_test, max, min 

if __name__ == '__main__':
    # --- THIS IS THE ONLY PLACE FOR PRINTS (for testing this script directly) ---
    args_batch_size = 2
    args_val_batch_size = 2
    args_data_root = '/Storage01/ShareData/radar_us/STR_US/counts/'
    args_num_workers = 0 
    
    args_data_year_to_use = 2020
    args_train_split = 0.8
    args_random_seed = 42

    print(f"--- dataloader.py direct test (Version 11.1 - MINIMAL PRINTS in __main__) ---")
    print(f"Test Settings: Year={args_data_year_to_use}, Data Root='{args_data_root}'")
    print(f"               BatchSize={args_batch_size}, ValBatchSize={args_val_batch_size}")

    train_loader, vali_loader, test_loader, mean, std = load_data(
        batch_size=args_batch_size,
        val_batch_size=args_val_batch_size,
        data_root=args_data_root,
        num_workers=args_num_workers,
        data_year=args_data_year_to_use,
        train_split_ratio=args_train_split,
        random_seed=args_random_seed
    )

    print(f"\n--- Train DataLoader Output (first few batches) ---")
    if train_loader: # Check if loader was created
        print(f"Train Dataset (Gremlin) samples: {len(train_loader.dataset) if train_loader.dataset else 0}")
        if hasattr(train_loader.dataset, 'xshape') and train_loader.dataset.xshape is not None:
            print(f"  Inferred xshape: {train_loader.dataset.xshape}, tshape: {train_loader.dataset.tshape}")
        
        # Iterate a few times as requested, without extra checks
        count = 0
        for input_batch, output_batch in train_loader: 
            print(f"Batch {count+1}: Input shape: {input_batch.shape}, Target shape: {output_batch.shape}")
            count += 1
            if count >= 6: 
                break 
        if count == 0 and len(train_loader.dataset) > 0:
             print("  Train DataLoader created but did not yield any batches (dataset might be smaller than batch_size or other issue).")
        elif count == 0:
            print("  Train DataLoader's dataset is empty.")

    else:
        print("Train DataLoader is None (no training files or dataset could not be initialized).")


    print(f"\n--- Validation DataLoader Output (first few batches) ---")
    if vali_loader:
        print(f"Validation Dataset (Gremlin) samples: {len(vali_loader.dataset) if vali_loader.dataset else 0}")
        if hasattr(vali_loader.dataset, 'xshape') and vali_loader.dataset.xshape is not None:
            print(f"  Inferred xshape: {vali_loader.dataset.xshape}, tshape: {vali_loader.dataset.tshape}")

        count = 0
        for input_batch, output_batch in vali_loader:
            print(f"Batch {count+1}: Input shape: {input_batch.shape}, Target shape: {output_batch.shape}")
            count += 1
            if count >= 2: 
                break
        if count == 0 and len(vali_loader.dataset) > 0 :
             print("  Validation DataLoader created but did not yield any batches.")
        elif count == 0:
            print("  Validation DataLoader's dataset is empty.")
    else:
        print("Validation DataLoader is None.")


    print(f"\nTest DataLoader: {test_loader}") 
    print(f"Returned mean: {mean}") 
    print(f"Returned std: {std}")