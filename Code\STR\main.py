import argparse
from exp import Exp
import warnings
warnings.filterwarnings('ignore')

def create_parser():
    parser = argparse.ArgumentParser()
    # 基本参数
    parser.add_argument('--res_dir', default='./520', type=str)
    parser.add_argument('--ex_name', default='UNet_hmse_lion_32_lr0.0003', type=str)
    parser.add_argument('--seed', default=42, type=int)
    
    # 数据参数
    parser.add_argument('--batch_size', default=32, type=int)
    parser.add_argument('--val_batch_size', default=8, type=int)
    parser.add_argument('--test_batch_size', default=1, type=int, help="测试时的批次大小，设为1则指标为算术平均") # 新增参数
    parser.add_argument('--data_root', default='/Storage01/ShareData/radar_us/STR_US/counts/')
    parser.add_argument('--dataname', default='us2020', choices=['us', 'china'])
    parser.add_argument('--num_workers', default=8, type=int)
    parser.add_argument('--in_shape', default=[4, 768, 1536], type=int, nargs='*')
    
    # UNet 模型参数
    parser.add_argument('--out_chans', default=1, type=int)
    parser.add_argument('--unet_channels', default=[32, 64, 128, 256], type=int, nargs='*')
    parser.add_argument('--unet_skip', default=True, type=bool)
    
    # 训练参数
    parser.add_argument('--epochs', default=100, type=int)
    parser.add_argument('--log_step', default=1, type=int)
    parser.add_argument('--lr', default=0.0003, type=float)
    parser.add_argument('--early_stopping_patience', default=15, type=int)
    parser.add_argument('--scheduler', default='cosine', type=str, choices=['constant', 'linear', 'cosine'])
    parser.add_argument('--optimizer', default='lion', type=str, choices=['adam', 'adamw', 'lion'])
    parser.add_argument('--warmup_steps', default=1000, type=int)
    #parser.add_argument('--eval_step', default=10, type=int, help="每隔多少个epoch进行一次完整的验证指标计算和打印") # 新增参数
    # loss函数选择
    parser.add_argument('--loss_type', default='hmse', type=str, 
                        choices=['mse', 'mae', 'wmse', 'wmae', 'hmse'],
                        help="Type of loss function to use (mse, mae, wmse, wmae，hmse).")
    # 测试指标
    parser.add_argument('--metrics_thresholds', default=[10, 20, 30, 40], type=int, nargs='*', 
                        help="用于计算 CSI, POD 等指标的原始物理尺度整数阈值列表。")
    # 混合精度训练参数
    parser.add_argument('--mixed_precision', type=str, default='fp16', choices=['no', 'fp16', 'bf16'])
    
    # 分布式训练参数
    parser.add_argument('--gradient_accumulation_steps', type=int, default=1)
    parser.add_argument('--gradient_clipping', type=float, default=1.0)

    return parser

if __name__ == '__main__':
    args = create_parser().parse_args()
    exp = Exp(args)
    print('>>>>>>>>>>>>>>>>>>>>>>>>>>>>  开始训练  <<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<')
    exp.train(args)
    print('>>>>>>>>>>>>>>>>>>>>>>>>>>>> 开始测试 <<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<')
    mse = exp.test(args)