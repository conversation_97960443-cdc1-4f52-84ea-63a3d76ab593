import torch
import torch.nn.functional as F

def test_bf16_precision():
    print("Testing bf16 precision for wavelet transform")
    
    # 简单的haar小波系数
    haar_lo = torch.tensor([0.7071067811865476, 0.7071067811865476])
    haar_hi = torch.tensor([-0.7071067811865476, 0.7071067811865476])
    
    # 创建2D滤波器
    dec_filters = torch.stack([
        haar_lo.unsqueeze(0) * haar_lo.unsqueeze(1),  # LL
        haar_lo.unsqueeze(0) * haar_hi.unsqueeze(1),  # LH  
        haar_hi.unsqueeze(0) * haar_lo.unsqueeze(1),  # HL
        haar_hi.unsqueeze(0) * haar_hi.unsqueeze(1)   # HH
    ], dim=0)
    
    rec_filters = torch.stack([
        haar_lo.unsqueeze(0) * haar_lo.unsqueeze(1),  # LL
        haar_lo.unsqueeze(0) * haar_hi.unsqueeze(1),  # LH
        haar_hi.unsqueeze(0) * haar_lo.unsqueeze(1),  # HL  
        haar_hi.unsqueeze(0) * haar_hi.unsqueeze(1)   # HH
    ], dim=0)
    
    # 扩展到多通道
    dec_filters = dec_filters[:, None].repeat(1, 1, 1, 1)
    rec_filters = rec_filters[:, None].repeat(1, 1, 1, 1)
    
    # 测试不同精度
    dtypes = [torch.float32, torch.bfloat16]
    
    for dtype in dtypes:
        print(f"\nTesting {dtype}")
        
        # 创建测试数据
        x = torch.randn(1, 1, 4, 4, dtype=dtype)
        dec_f = dec_filters.to(dtype)
        rec_f = rec_filters.to(dtype)
        
        print(f"Input: {x.flatten()}")
        
        # 前向变换
        x_wt = F.conv2d(x, dec_f, stride=2, groups=1)
        x_wt = x_wt.reshape(1, 1, 4, 2, 2)
        
        print(f"Wavelet coeffs shape: {x_wt.shape}")
        print(f"LL: {x_wt[0,0,0,:,:].flatten()}")
        
        # 逆变换
        x_flat = x_wt.reshape(1, 4, 2, 2)
        x_recon = F.conv_transpose2d(x_flat, rec_f, stride=2, groups=1)
        
        print(f"Reconstructed: {x_recon.flatten()}")
        
        # 计算误差
        error = (x_recon - x).abs().max().item()
        print(f"Max reconstruction error: {error:.2e}")
        
        if error < 1e-5:
            print("✅ Perfect reconstruction")
        elif error < 1e-2:
            print("⚠️ Acceptable reconstruction")  
        else:
            print("❌ Poor reconstruction")

if __name__ == "__main__":
    test_bf16_precision()
