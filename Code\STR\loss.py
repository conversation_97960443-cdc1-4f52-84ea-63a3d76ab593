import torch
import torch.nn as nn
import torch.nn.functional as F



class L1Loss(nn.Module):
    def __init__(self):
        super(L1Loss, self).__init__()

    def forward(self, pred, obs):
        return F.l1_loss(pred, obs, reduction='mean')

class MSELoss(nn.Module):
    def __init__(self):
        super(MSELoss, self).__init__()

    def forward(self, pred, obs):
        return F.mse_loss(pred, obs, reduction='mean')

class WMSELoss(nn.Module):
    def __init__(self):
        super().__init__()

    def forward(self, y_pred, y_true):
        """
        Calculates Weighted Mean Squared Error Loss in PyTorch (channels-first).
        
        Args:
            y_true (torch.Tensor): True values, shape (B, C, H, W).
            y_pred (torch.Tensor): Predicted values, shape (B, C, H, W).

        Returns:
            torch.Tensor: Loss tensor, shape (B, H, W).
        """
        device = y_true.device  # 获取 y_true (或 y_pred) 的设备
        # 根据预测值与真实值的差异设置不同的权重
        weights = torch.where(y_pred >= y_true,
                              torch.tensor(1.0, device=device),  # 当预测大于真实值时，权重为 1
                              torch.where(y_true >= 0.5, torch.tensor(3.0, device=device), torch.tensor(2.0, device=device)))  # 其他情况权重为 2 或 3
        
        diff = torch.square(y_pred - y_true)  # 计算平方误差
        loss = torch.mean(weights * diff, dim=1)  # 对每个像素计算加权平方误差
        scalar_loss = torch.mean(loss)  # 对整个批次求平均损失
        return scalar_loss

class WMAELoss(nn.Module):
    def __init__(self):
        super().__init__()  # 调用 nn.Module 的构造函数

    #def forward(self, y_true, y_pred):
    def forward(self, y_pred, y_true):
        """
        Calculates Weighted Mean Absolute Error Loss in PyTorch (channels-first).

        Args:
            y_true (torch.Tensor): True values, shape (B, C, H, W).
            y_pred (torch.Tensor): Predicted values, shape (B, C, H, W).

        Returns:
            torch.Tensor: Loss tensor, shape (B, H, W).
        """
        device = y_true.device  # 获取 y_true (或 y_pred) 的设备
        weights = torch.where(y_pred >= y_true,
                              torch.tensor(1.0, device=device),  # 将常量张量移动到同一设备
                              torch.where(y_true >= 0.5, torch.tensor(3.0, device=device), torch.tensor(2.0, device=device))) # 将常量张量移动到同一设备
        diff = torch.abs(y_pred - y_true)
        loss = torch.mean(weights * diff, dim=1)
        scalar_loss = torch.mean(loss)
        return scalar_loss 

class HMSELoss(nn.Module):
    def __init__(self, w0=5.0, w1=4.0):
        super(HMSELoss, self).__init__()
        self.w0 = w0
        self.w1 = w1

    def forward(self, pred, target):
        """
        Calculates Weighted Mean Squared Error Loss (Hilburn et al., 2021).

        Args:
            pred (torch.Tensor): Predicted values, shape (B, 1, H, W).
            target (torch.Tensor): True values, shape (B, 1, H, W).

        Returns:
            torch.Tensor: Scalar loss value.
        """
        squared_error = (pred - target) ** 2
        weights = torch.clamp(torch.exp(self.w0 * torch.pow(target, self.w1)), max=1e6)
        weighted_loss = weights * squared_error
        loss = torch.mean(weighted_loss)  # 直接使用 torch.mean
        return loss







