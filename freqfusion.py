import torch
import torch.nn as nn
import torch.nn.functional as F

def fft(input):
    '''
    高精度FFT函数：
    - 自动检测输入精度并适配
    - FFT计算使用Float32确保精度
    - 🎯 幅度谱和相位谱都输出Float32，确保最高精度

    输入维度：input: (B, C, H, W) - 批次大小、通道数、高度、宽度
    输出维度：amp: (B, C, H, W//2+1) [Float32], pha: (B, C, H, W//2+1) [Float32]
    '''
    # 🎯 内存优化：就地转换，避免创建副本
    if input.dtype == torch.bfloat16:
        # 临时转换为Float32进行FFT计算
        with torch.amp.autocast('cuda', enabled=False):
            input_f32 = input.float()
            # 执行FFT计算
            img_fft = torch.fft.rfftn(input_f32, dim=(-2, -1))
            # 立即释放input_f32的内存
            del input_f32
    else:
        # 如果已经是Float32，直接使用
        img_fft = torch.fft.rfftn(input, dim=(-2, -1))

    # 计算幅度谱和相位谱
    amp = torch.abs(img_fft)  # 幅度谱
    pha = torch.angle(img_fft)  # 相位谱

    # 立即释放复数FFT结果的内存
    del img_fft

    # 🎯 修改后的精度策略：
    # 1. 幅度谱也保持Float32确保精度 (根据用户要求)
    # 2. 相位谱保持Float32确保精度
    # 两者都使用Float32，确保最高精度
    # amp保持float32  # 幅度也保持高精度
    # pha保持float32  # 相位保持高精度

    return amp, pha
    
class AmpFuse(nn.Module):
    def __init__(self, in_channels=1):
        super().__init__()
        self.in_channels = in_channels
        self.conv1 = nn.Sequential(
            nn.Conv2d(2 * in_channels, in_channels, kernel_size=3, stride=1, padding=1),
            nn.LeakyReLU(0.1),
            nn.Conv2d(in_channels, in_channels, kernel_size=3, stride=1, padding=1),
            nn.LeakyReLU(0.1),
        )

    def forward(self, f1, f2):
        # f1, f2: (B, C, H, W//2+1) - 现在幅度谱也是Float32
        # 确保输入精度一致
        if f1.dtype != f2.dtype:
            # 如果精度不同，统一转换为较高精度
            target_dtype = torch.float32 if (f1.dtype == torch.float32 or f2.dtype == torch.float32) else f1.dtype
            f1 = f1.to(target_dtype)
            f2 = f2.to(target_dtype)

        x = torch.cat([f1, f2], dim=1)  # (B, 2*C, H, W//2+1)
        x = self.conv1(x)  # (B, C, H, W//2+1)
        return x

class PhaFuse(nn.Module):
    def __init__(self, in_channels=1):
        super().__init__()
        self.in_channels = in_channels
        self.conv1 = nn.Sequential(
            nn.Conv2d(2 * in_channels, in_channels, kernel_size=3, stride=1, padding=1),
            nn.LeakyReLU(0.1),
            nn.Conv2d(in_channels, in_channels, kernel_size=3, stride=1, padding=1),
            nn.LeakyReLU(0.1),
        )

    def forward(self, f1, f2):
        # f1, f2: (B, C, H, W//2+1) - 现在相位谱也是Float32
        # 确保输入精度一致
        if f1.dtype != f2.dtype:
            # 相位融合保持高精度
            target_dtype = torch.float32
            f1 = f1.to(target_dtype)
            f2 = f2.to(target_dtype)

        x = torch.cat([f1, f2], dim=1)  # (B, 2*C, H, W//2+1)
        x = self.conv1(x)  # (B, C, H, W//2+1)
        return x



class IFFT(nn.Module):
    def __init__(self, in_channels=1, out_channels=8):
        super().__init__()
        self.in_channels = in_channels
        self.conv1 = nn.Sequential(
            nn.Conv2d(in_channels+2, out_channels, kernel_size=3, stride=1, padding=1),
            nn.ReLU(),
            nn.Conv2d(out_channels, out_channels, kernel_size=3, stride=1, padding=1),
            nn.ReLU(),
        )

    def forward(self, amp, pha):
        # amp: 现在也是Float32, pha: Float32
        # 🎯 现在幅度谱和相位谱都是Float32，确保最高精度
        amp_f32 = amp.float() if amp.dtype != torch.float32 else amp
        pha_f32 = pha.float() if pha.dtype != torch.float32 else pha

        real = amp_f32 * torch.cos(pha_f32) + 1e-8  # (B, C, H, W//2+1)
        imag = amp_f32 * torch.sin(pha_f32) + 1e-8  # (B, C, H, W//2+1)
        x = torch.complex(real, imag)  # (B, C, H, W//2+1)
        x = torch.abs(torch.fft.irfftn(x, dim=(-2, -1)))  # (B, C, H, W)

        # 🎯 输出精度策略：
        # 现在幅度谱和相位谱都是Float32，IFFT输出也保持Float32
        # 最终的精度调整在FreqFusion的forward方法中进行
        # 对通道维度进行聚合操作，添加全局特征
        x_max = torch.max(x, 1)[0].unsqueeze(1)  # (B, 1, H, W)
        x_mean = torch.mean(x, 1).unsqueeze(1)  # (B, 1, H, W)
        # 将原始通道信息与聚合信息结合
        x = torch.cat([x, x_max, x_mean], dim=1)  # (B, C+2, H, W)
        x = self.conv1(x)  # (B, out_channels, H, W)
        return x

class FreqFusion(nn.Module):
    def __init__(self, in_channels=1, out_channels=8):
        super().__init__()
        self.in_channels = in_channels
        self.f1bn = nn.BatchNorm2d(self.in_channels)
        self.f2bn = nn.BatchNorm2d(self.in_channels)
        self.ampfuse = AmpFuse(in_channels)
        self.phafuse = PhaFuse(in_channels)
        self.ifft = IFFT(in_channels, out_channels)
    
    def forward(self, f1, f2):
        # f1, f2: (B, C, H, W)
        # 🎯 记住原始输入的数据类型，用于决定最终输出精度
        f1 = self.f1bn(f1)
        f2 = self.f2bn(f2)
        original_dtype = f1.dtype

        # 🎯 内存优化：提取频域信息
        f1_amp, f1_pha = fft(f1)  # (B, C, H, W//2+1) - 都是Float32
        f2_amp, f2_pha = fft(f2)  # (B, C, H, W//2+1) - 都是Float32

        # 🎯 内存优化：融合幅度谱和相位谱
        fused_amp = self.ampfuse(f1_amp, f2_amp)  # (B, C, H, W//2+1)
        # 立即释放不再需要的幅度谱内存
        del f1_amp, f2_amp

        fused_pha = self.phafuse(f1_pha, f2_pha)  # (B, C, H, W//2+1)
        # 立即释放不再需要的相位谱内存
        del f1_pha, f2_pha

        # 逆FFT重构
        result = self.ifft(fused_amp, fused_pha)  # (B, out_channels, H, W)

        # 🎯 根据原始输入类型调整输出精度
        if original_dtype == torch.bfloat16:
            result = result.bfloat16()
        elif original_dtype == torch.float16:
            result = result.half()
        # 如果原始是Float32，保持Float32

        return result


if __name__ == '__main__':
    # 测试单通道情况
    print("测试单通道 (C=1):")
    f1 = torch.randn(1, 1, 256, 256)
    f2 = torch.randn(1, 1, 256, 256)
    model = FreqFusion(in_channels=1, out_channels=8)
    result = model(f1, f2)
    print(f"输入: {f1.shape}, {f2.shape} -> 输出: {result.shape}")
    
    # 测试多通道情况
    print("\n测试多通道 (C=3):")
    f1_rgb = torch.randn(1, 3, 256, 256)
    f2_rgb = torch.randn(1, 3, 256, 256)
    model_rgb = FreqFusion(in_channels=3, out_channels=8)
    result_rgb = model_rgb(f1_rgb, f2_rgb)
    print(f"输入: {f1_rgb.shape}, {f2_rgb.shape} -> 输出: {result_rgb.shape}")
    
    # 测试更多通道情况
    print("\n测试更多通道 (C=64):")
    f1_feat = torch.randn(2, 64, 128, 128)
    f2_feat = torch.randn(2, 64, 128, 128)
    model_feat = FreqFusion(in_channels=64, out_channels=64)
    result_feat = model_feat(f1_feat, f2_feat)
    print(f"输入: {f1_feat.shape}, {f2_feat.shape} -> 输出: {result_feat.shape}")