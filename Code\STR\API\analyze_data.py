import os
import numpy as np
import glob
from tqdm import tqdm

def _collect_files_for_year(data_root, year):
    all_files = []
    year_path = os.path.join(data_root, str(year))
    if not os.path.isdir(year_path):
        return []
    found_in_year = False
    for month_num in range(1, 13):
        month_str = f"{month_num:02d}"
        month_path = os.path.join(year_path, month_str)
        if os.path.isdir(month_path):
            pattern = os.path.join(month_path, "*.npz")
            month_files = glob.glob(pattern)
            if month_files:
                all_files.extend(month_files)
                found_in_year = True
    if found_in_year:
        all_files.sort()
    return all_files

def analyze_radar_data(data_root, year=2020):
    # 获取所有文件
    files = _collect_files_for_year(data_root, year)
    if not files:
        print(f"未找到{year}年的数据文件")
        return
    
    # 定义阈值和百分比
    thresholds = [10, 20, 30, 40]
    percentages = [0.05, 0.10, 0.20]
    
    # 创建results目录
    results_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'results')
    os.makedirs(results_dir, exist_ok=True)
    
    # 用于存储结果的字典
    results = {}
    for threshold in thresholds:
        results[threshold] = {}
        for percentage in percentages:
            results[threshold][percentage] = []
    
    # 分析每个文件
    print(f"正在分析{len(files)}个文件的数据分布...")
    for file_path in tqdm(files):
        try:
            with np.load(file_path) as data:
                ydata = data['ydata']
                # 反归一化处理
                ydata = ydata * 60
                total_pixels = ydata.size
                
                # 计算每个阈值下的占比
                for threshold in thresholds:
                    # 将阈值也进行归一化处理
                    normalized_threshold = threshold
                    pixels_above_threshold = np.sum(ydata > normalized_threshold)
                    ratio = pixels_above_threshold / total_pixels
                    
                    # 检查是否超过各个百分比阈值
                    for percentage in percentages:
                        if ratio > percentage:
                            results[threshold][percentage].append(file_path)
        except Exception as e:
            print(f"处理文件 {file_path} 时出错: {e}")
            continue
    
    # 打印分析结果并保存到文件
    print("\n数据分析结果:")
    for threshold in thresholds:
        print(f"\n回波值 > {threshold} 的统计结果:")
        for percentage in percentages:
            file_count = len(results[threshold][percentage])
            print(f"  占比超过 {percentage*100:>4.1f}% 的样本数量: {file_count:>4d}")
            
            # 保存结果到txt文件
            result_file = os.path.join(results_dir, f'threshold_{threshold}_ratio_{percentage:.2f}.txt')
            with open(result_file, 'w', encoding='utf-8') as f:
                for file_path in results[threshold][percentage]:
                    f.write(f"{os.path.basename(file_path)}\n")
            
            if file_count > 0:
                print(f"  完整结果已保存到: {os.path.basename(result_file)}")

if __name__ == '__main__':
    data_root = '/Storage01/ShareData/radar_us/STR_US/counts/'
    analyze_radar_data(data_root,2020)